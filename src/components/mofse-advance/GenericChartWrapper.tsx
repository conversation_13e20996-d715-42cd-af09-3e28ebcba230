"use client";
import React, { useState, useMemo } from "react";
import { ToggleGroup, ToggleGroupItem } from "@/components/ui/toggle-group";
import { cn } from "@/lib/utils";
import {
  useGenericMofseAdvanceCharts
} from "@/lib/state";
import { calculateDateRange } from "./utils";
import Loader from "../comman/Loader";
import GenericBarGraph, { DataPoint } from "./GenericBarGraph";
import Image from "next/image";
import { ListFilter } from "lucide-react";


// Time range options
const timeRangeOptions = ["24h", "7d", "1m"];

const GenericChartWrapper = ({
  assetType,
  filter,
}: {
  assetType: string;
  filter: string;
}) => {

  const [selectedBlockchain, setSelectedBlockchain] = useState<string>("");

  const [timeRange, setTimeRange] = useState<string>("1m");
  const showDay = ["24h", "7d", "1m"].includes(timeRange);
  const showMonth = ["24h", "7d", "1m", "6m", "3y", "1y", '5y'].includes(timeRange);
  const showYear = ["24h", "7d", "6m", "1y", "3y", "5y"].includes(timeRange);

  const { startDate, endDate } = useMemo(
    () => calculateDateRange(timeRange),
    [timeRange]
  );

  const genericMofseAdvanceChartsQuery = useGenericMofseAdvanceCharts(
    startDate,
    endDate,
    assetType,
    filter,
    selectedBlockchain
  );

  function getTransactionData() {
    return { 
        data: genericMofseAdvanceChartsQuery.data?.map((item) => ({
        date: item.date,
        count: item.count,
        })),
        isLoading: genericMofseAdvanceChartsQuery.isLoading,
    };
  }

  const { data, isLoading } = getTransactionData();

  const formattedData: DataPoint[] =
    data?.map((item) => ({
      date: item.date,
      count: item.count,
      timestamp: new Date(item.date).getTime(),
    })) || [];

  const chartData = formattedData.sort((a, b) => a.timestamp - b.timestamp);

  return (
    <div className="flex flex-col space-y-6">
      <div className="flex justify-end items-center">
        <div className="flex space-x-2">
          <ToggleGroup
            type="single"
            value={timeRange}
            onValueChange={(value) => value && setTimeRange(value)}
            className="flex rounded overflow-hidden  border border-border-light px-2 py-1 gap-2"
          >
            {timeRangeOptions.map((option) => (
              <ToggleGroupItem
                key={option}
                value={option}
                className={cn(
                  "outline-none border-none px-3 rounded last:rounded-tr last:rounded-br first:rounded-tl first:rounded-bl"
                )}
              >
                {option}
              </ToggleGroupItem>
            ))}
          </ToggleGroup>
        </div>
      </div>

      {/* Chart */}
      {isLoading ? (
        <Loader />
      ) : (
        <GenericBarGraph
          data={chartData}
          coin={assetType}
          options={{
            showDay,
            showMonth,
            showYear,
          }}
          filter={filter}
        />
      )}
     {/* Blockchain Selection UI */}
      <div className="flex flex-col space-y-4">
        <div className="text-lg font-semibold text-white flex items-center gap-2"><ListFilter/> <span>Blockchains </span></div>
        <div className="flex flex-wrap gap-3">
          {BLOCKCHAINS.map((chain) => (
            <button
              key={chain.value}
              onClick={() => setSelectedBlockchain(chain.value)}
              className={cn(
                "flex items-center space-x-2 px-4 py-2 rounded-md text-sm font-medium transition-colors",
                "border border-gray-600 bg-transparent text-gray-300",
                "hover:bg-bg-primary hover:text-black hover:border-bg-primary",
                selectedBlockchain === chain.value
                  ? "bg-bg-primary text-black border-bg-primary"
                  : "",
                "focus:outline-none focus:ring-2 focus:ring-bg-primary focus:ring-offset-2 focus:ring-offset-gray-800"
              )}
            >
              {chain.icon && <Image
                src={chain.icon}
                alt={chain.label}
                width={20}
                height={20}
                className="shrink-0"
              />}
              <span>{chain.label}</span>
            </button>
          ))}
        </div>
      </div>
    </div>
  );
};

export default GenericChartWrapper;

const BLOCKCHAINS = [
  { value: "", label: "All", icon: "" },
//   { value: "Aptos", label: "Aptos", icon: "/blockchains/aptos.svg" },
  { value: "Ethereum", label: "Ethereum", icon: "/blockchains/eth.svg" },
//   { value: "Tron", label: "Tron", icon: "/blockchains/trx.svg" },
//   { value: "Solana", label: "Solana", icon: "/blockchains/sol.svg" },
//   { value: "BSC", label: "BSC", icon: "/blockchains/bnb.svg" }, 
  { value: "Avalanche", label: "Avalanche", icon: "/blockchains/avax.svg" },
  { value: "Arbitrum", label: "Arbitrum", icon: "/blockchains/arbitrum.svg" },
  { value: "Optimism", label: "Optimism", icon: "/blockchains/optimism.svg" },
  { value: "Polygon", label: "Polygon", icon: "/blockchains/matic.svg" }, 
  { value: "Base", label: "Base", icon: "/blockchains/base.svg" },
  { value: "BNB", label: "BNB", icon: "/blockchains/bnb.svg" },
//   { value: "Base", label: "Base", icon: "/blockchains/base.svg" },
//   { value: "Celo", label: "Celo", icon: "/blockchains/celo.svg" },
];
