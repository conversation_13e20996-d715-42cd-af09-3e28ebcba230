"use client";
import React, { useState, useMemo } from "react";
import { ToggleGroup, ToggleGroupItem } from "@/components/ui/toggle-group";
import { cn } from "@/lib/utils";
import {
  // useGenericMofseAdvanceCharts,
  useUsdThresholdTransactions,
  // useUsdThresholdTransactions,
} from "@/lib/state";

import Loader from "../comman/Loader";

import { calculateDateRange } from "../mofse-advance/utils";
import { DataPoint } from "../mofse-advance/TransactionCountHistoryChart";
import TransactionCountHistoryBarGraph from "./TransactionCountHistoryBarGraph";
import { getAssetName } from "./utils";
import { Coin } from "@/lib/api.interface";
import FullscreenWrapper from "../comman/FullscreenWrapper";

// Time range options
// const timeRangeOptions = ["24h", "7d", "1m"];

const USDThresholdChart = ({
  coin,
  onChainFilter
}: {
  coin: Coin | null;
  onChainFilter: string;
}) => {
  const [timeRange, setTimeRange] = useState<string>(
    ['btc', 'eth'].includes(coin?.symbol?.toLowerCase() || '') ? "5y" : "1m"
  );
  // const showDate = ["7d", "1m"].includes(timeRange);

  const timeRangeOptions = useMemo(() => {
    const baseOptions = ["24h", "7d", "1m"];
    // If the coin is Bitcoin or Ethereum, add more granular time options
    if (['btc', 'eth'].includes(coin?.symbol?.toLowerCase() || '')) {
      return [...baseOptions, "6m", "1y", "3y", "5y"];
    }
    return baseOptions;
  }, [coin?.symbol]);

  // Show date on the x-axis for any range longer than 24 hours
  const showDay = ["24h", "7d", "1m"].includes(timeRange);
  const showMonth = ["24h", "7d", "1m", "6m", "3y", "1y", '5y'].includes(timeRange);
  const showYear = ["24h", "7d", "6m", "1y", "3y", "5y"].includes(timeRange);

  const { startDate, endDate } = useMemo(
    () => calculateDateRange(timeRange),
    [timeRange]
  );

  const genericMofseAdvanceChartsQuery = useUsdThresholdTransactions(
      startDate,
      endDate,
      getAssetName(coin?.symbol || ""),
      onChainFilter
    );

  // const usdThresholdTransactionQuery = useUsdThresholdTransactions(
  //   startDate,
  //   endDate,
  //   getAssetName(assetType),
  //   onChainFilter
  // );

  function getTransactionData() {
      return {
        data: genericMofseAdvanceChartsQuery.data,
        isLoading: genericMofseAdvanceChartsQuery.isLoading,
      };
  }

  const { data, isLoading } = getTransactionData();

  const formattedData: DataPoint[] =
    data?.map((item) => {
      let count = item.count;
        if(['avg-transaction-value', 'total-fee'].includes(onChainFilter)){
          count = item.count * (Number(coin?.price ) ?? 1);
        }
      return {
        date: item.date,
        count: count,
        timestamp: new Date(item.date).getTime()
      }
    }) || [];

  const chartData = formattedData.sort((a, b) => a.timestamp - b.timestamp);

  const isDataEmpty = useMemo(() => {
    if (!chartData || chartData.length === 0) {
      return true;
    }
    // Returns true if every single data point has a count of 0
    return chartData.every((item) => item.count === 0);
  }, [chartData]);

  return (
    <FullscreenWrapper title={`${getAssetName(coin?.symbol || '')} Transaction Chart`}>
      <div className="flex flex-col space-y-6">
        <div className="flex justify-end items-center">
          <div className="flex space-x-2">
            <ToggleGroup
              type="single"
              value={timeRange}
              onValueChange={(value) => value && setTimeRange(value)}
              className="flex rounded overflow-hidden  border border-border-light px-2 py-1 gap-2 text-white "
            >
              {timeRangeOptions.map((option) => (
                <ToggleGroupItem
                  key={option}
                  value={option}
                  className={cn(
                    "outline-none border-none px-3 rounded last:rounded-tr last:rounded-br first:rounded-tl first:rounded-bl "
                  )}
                >
                  {option}
                </ToggleGroupItem>
              ))}
            </ToggleGroup>
          </div>
        </div>

        {/* Chart */}
       {isLoading ? (
          <Loader />
        ) : isDataEmpty ? (
          <div className="flex items-center justify-center h-72 rounded-lg bg-background-light">
            <p className="text-sm text-text-secondary">No data available for this period.</p>
          </div>
        ) : (
          <TransactionCountHistoryBarGraph
            data={chartData}
            coin={coin}
            options={{
              showDay,
              showMonth,
              showYear,
            }}
            onChainFilter={onChainFilter}
          />
        )}
      </div>
    </FullscreenWrapper>
  );
};

export default USDThresholdChart;