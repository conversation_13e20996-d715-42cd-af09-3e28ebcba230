export function getAlertEmojis(usdAmount: string) {
  const amount = Math.round(+usdAmount);

  if (amount >= 10000000) return "🔴 🔴 🔴 🔴 🔴 🔴 🔴";
  if (amount >= 5000000) return "🔴 🔴 🔴 🔴 🔴 🔴";
  if (amount >= 1000000) return "🔴 🔴 🔴 🔴 🔴"; // $1M+
  if (amount >= 500000) return "🔴 🔴 🔴 🔴"; // $500K+
  if (amount >= 100000) return "🔴 🔴 🔴"; // $100K+
  if (amount >= 10000) return "🔴 🔴"; // $10K+
  return "🔴"; // Less than $10K
}

export function getIconAndSymbol(assetType: string) {
  let iconUrl = "";
  let symbol = "";

  switch (assetType) {
    case "bitcoin":
      iconUrl = "https://cdn.coinranking.com/bOabBYkcX/bitcoin_btc.svg";
      symbol = "BTC";
      break;
    case "ethereum":
      iconUrl = "https://cdn.coinranking.com/rk4RKHOuW/eth.svg";
      symbol = "ETH";
      break;
    case "usdt":
      iconUrl = "https://cdn.coinranking.com/iqfxcAEV1/tether-usd-usdt.svg";
      symbol = "USDT";
      break;
    case "usdc":
      iconUrl = "https://cdn.coinranking.com/jkDf8sQbY/usdc.svg";
      symbol = "USDC";
      break;
    default:
      iconUrl = "";
      symbol = "";
      break;
  }

  return { iconUrl, symbol };
}

export function getAssetName(asset: string) {
  switch (asset) {
    case 'BTC':
      return 'bitcoin';
    case 'ETH':
    return 'ethereum';
    case 'USDT':
    return 'usdt';
    case 'USDC':
    return 'usdc';
    case 'BNB':
    return 'bnb';
    case 'SOL':
    return 'solana';
    default:
      return asset.toLowerCase()
  }
}

export const formatYAxisTick = (tick: number) => {
  if (tick >= 1_000_000_000) {
    return `${(tick / 1_000_000_000).toFixed(1).replace(/\.0$/, "")}B`;
  }
  if (tick >= 1_000_000) {
    return `${(tick / 1_000_000).toFixed(1).replace(/\.0$/, "")}M`;
  }
  if (tick >= 1_000) {
    return `${(tick / 1_000).toFixed(1).replace(/\.0$/, "")}k`;
  }
  return tick.toString();
};