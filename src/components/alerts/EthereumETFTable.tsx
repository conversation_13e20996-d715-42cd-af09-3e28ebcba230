"use client";

import { useCoinglassData } from "@/lib/state";
import { ConfigProvider, Table, TableProps } from "antd";
import { useState } from "react";
import FullscreenWrapper from "../comman/FullscreenWrapper";

interface EthereumETFData {
  ticker: string;
  name: string;
  region: string;
  market_status: string;
  primary_exchange: string;
  cik_code: string;
  type: string;
  market_cap: string;
  list_date: number;
  shares_outstanding: string;
  aum_usd: string;
  management_fee_percent: string;
  last_trade_time: number;
  last_quote_time: number;
  volume_quantity: number;
  volume_usd: number;
  price: number;
  price_change: number;
  price_change_percent: number;
  asset_info: {
    nav: number;
    premium_discount: number;
    holding_quantity: number;
    change_percent_1d: number;
    change_quantity_1d: number;
    change_percent_7d: number;
    change_quantity_7d: number;
    date: string;
  };
  update_time: number;
}

export const EthereumETFTable = () => {
  const { data: etfData, isLoading, error } = useCoinglassData('ethereum-etf');
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(15);

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(value);
  };

  const formatLargeNumber = (value: number) => {
    if (value >= 1e9) {
      return `$${(value / 1e9).toFixed(2)}B`;
    } else if (value >= 1e6) {
      return `$${(value / 1e6).toFixed(2)}M`;
    } else if (value >= 1e3) {
      return `$${(value / 1e3).toFixed(2)}K`;
    }
    return formatCurrency(value);
  };

  const formatPercentage = (value: number) => {
    const isPositive = value >= 0;
    return (
      <span className={isPositive ? 'text-green-600' : 'text-red-600'}>
        {isPositive ? '+' : ''}{value.toFixed(2)}%
      </span>
    );
  };

  const ethereumETFColumns: TableProps<EthereumETFData>["columns"] = [
    {
      title: "#",
      key: "index",
      width: 50,
      align: "center",
      render: (_, __, index) => {
        return (currentPage - 1) * pageSize + index + 1;
      },
    },
    {
      title: "Ticker",
      dataIndex: "ticker",
      key: "ticker",
      width: 80,
      align: "left",
      render: (text) => (
        <span className="font-normal text-text-primary">{text || '-'}</span>
      ),
    },
    {
      title: "ETF Name",
      dataIndex: "fund_name",
      key: "fund_name",
      width: 200,
      align: "left",
      render: (text) =>  <span className="font-normal text-nowrap text-text-primary">{text || '-'}</span>,
    },
    {
      title: "Price",
      dataIndex: "price_usd",
      key: "price_usd",
      width: 100,
      align: "right",
      render: (value) => value ? formatCurrency(value) : '-',
    },
    {
      title: "Price Change",
      dataIndex: "price_change_usd",
      key: "price_change",
      width: 120,
      align: "right",
      render: (value, record) => (
        <div className="flex flex-col">
          <span>{value ? formatCurrency(value) : '-'}</span>
          {record.price_change_percent ? formatPercentage(record.price_change_percent) : '-'}
        </div>
      ),
    },
    {
      title: "Volume",
      dataIndex: "volume_usd",
      key: "volume_usd",
      width: 120,
      align: "right",
      render: (value) => value ? formatLargeNumber(value) : '-',
    },
    {
      title: "Volume",
      dataIndex: "volume_quantity",
      key: "volume_quantity",
      width: 120,
      align: "right",
      render: (value) => value ? value.toLocaleString() : '-',
    },
    {
      title: "Turnover Rate",
      key: "turnover_rate",
      width: 120,
      align: "right",
      render: (_, record) => {
        const volumeUSD = record.volume_usd;
        const aumUSD = parseFloat(record.aum_usd);

        if (!volumeUSD || !aumUSD || aumUSD === 0) return '-';

        const turnoverRate = (volumeUSD / aumUSD) * 100;
        return `${turnoverRate.toFixed(2)}%`;
      },
    },
    {
      title: "Shares Outstanding",
      dataIndex: "shares_outstanding",
      key: "shares_outstanding",
      width: 140,
      align: "right",
      render: (value) => value ? parseInt(value).toLocaleString() : '-',
    },
    {
      title: "AUM",
      dataIndex: "aum_usd",
      key: "aum_usd",
      width: 120,
      align: "right",
      render: (value) => value ? formatLargeNumber(parseFloat(value)) : '-',
    },
    {
      title: "Market Cap",
      dataIndex: "market_cap_usd",
      key: "market_cap_usd",
      width: 120,
      align: "right",
      render: (value) => value ? formatLargeNumber(parseFloat(value)) : '-',
    },
    // {
    //   title: "Expense Ratio",
    //   dataIndex: "asset_details",
    //   key: "expense_ratio",
    //   width: 120,
    //   align: "right",
    //   render: () => {
    //     // Since management_fee_percent is not in the data structure,
    //     // we'll show a placeholder or calculate from available data
    //     return '-'; // No expense ratio data available in current structure
    //   },
    // },
    {
      title: "Status",
      dataIndex: "market_status",
      key: "market_status",
      width: 100,
      align: "left",
      render: (value) => value || '-',
    },
  ];

  if (error) {
    return (
      <div className="p-8">
        <p className="text-red-600">Error loading Ethereum ETF data</p>
      </div>
    );
  }

  return (
    <FullscreenWrapper title="Ethereum ETF Table">
      <section className="mr-4">

          <ConfigProvider
            theme={{
              components: {
                Spin: {
                  colorPrimary: "#bbd955",
                },
                Table: {
                  fontFamily: "DM Sans",
                  colorPrimary: "#bbd955",
                },
              },
            }}
          >
            <Table
              columns={ethereumETFColumns}
              dataSource={etfData || []}
              rowKey="ticker"
              loading={isLoading}
              scroll={{ x: "800px" }}
              pagination={{
                current: currentPage,
                pageSize: pageSize,
                onChange: (page, size) => {
                  setCurrentPage(page);
                  setPageSize(size || 15);
                },
                showSizeChanger: true,
                showQuickJumper: false,
                showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items`,
              }}
              className="crypto-table"
            />
          </ConfigProvider>
          <div className="pb-12" />

      </section>
    </FullscreenWrapper>
  );
};
