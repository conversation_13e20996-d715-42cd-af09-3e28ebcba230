"use client";
import { formatChartDate } from "@/lib/utils";
import {
  Line<PERSON>hart,
  Line,
  XAxis,
  <PERSON><PERSON><PERSON>s,
  CartesianGrid,
  <PERSON>lt<PERSON>,
  ResponsiveContainer,
} from "recharts";

export const COLORS = {
  BITCOIN: "#F68B1F",
  GOLD: "#E3E11B",
  SP500: "#133D9E",
  GRID: "#5d5e5f",
  TEXT: "#fff",
};

export type DataPoint = {
  time: string;
  bitcoin: number;
  gold: number;
  sp500: number;
};

const BTCvsGOLDvsSP500 = ({
  data,
  isLastMonth,
}: {
  data: DataPoint[];
  isLastMonth: boolean;
}) => {
  return (
    <div
      style={{
        width: "100%",
        height: 310,
        borderRadius: "12px",
      }}
    >
      <ResponsiveContainer width="100%" height="100%">
        <LineChart
          data={data}
          margin={{ top: 20, right: 12, left: 22, bottom: 0 }}
        >
          <CartesianGrid stroke={COLORS.GRID} strokeDasharray="3 3" />
          <XAxis
            dataKey="time"
            tick={{ fill: COLORS.TEXT, fontSize: 12 }}
            tickFormatter={(value) => {
              return formatChartDate(value, { showDay: isLastMonth });
            }}
            tickLine={false}
            axisLine={false}
          />
          <YAxis
            yAxisId="left"
            domain={[0, "auto"]}
            tick={{ fill: COLORS.TEXT, fontSize: 12 }}
            tickLine={false}
            axisLine={false}
            tickFormatter={(value) => `$${value.toLocaleString()}`}
            label={{
              value: "BITCOIN (USD)",
              angle: -90,
              position: "insideLeft",
              offset: -15,
              style: { fill: COLORS.BITCOIN, fontSize: 15, fontWeight: 500 },
            }}
          />
          <YAxis
            yAxisId="right"
            orientation="right"
            domain={[0, "auto"]}
            tick={{ fill: COLORS.TEXT, fontSize: 12 }}
            tickLine={false}
            axisLine={false}
            label={{
              value: "Gold and S&P 500",
              angle: 90,
              position: "insideRight",
              offset: -5,
              style: { fill: COLORS.SP500, fontSize: 15, fontWeight: 500 },
            }}
          />
          <Tooltip
            formatter={(val: number, name: string) => [
              `${val.toLocaleString()}`,
              name,
            ]}
            labelFormatter={(label) => `Time: ${label}`}
            contentStyle={{
              color: "#000",
            }}
          />
          <Line
            type="monotone"
            yAxisId="left"
            dataKey="bitcoin"
            stroke={COLORS.BITCOIN}
            strokeWidth={2}
            dot={false}
            name="Bitcoin"
          />
          <Line
            type="monotone"
            yAxisId="right"
            dataKey="gold"
            stroke={COLORS.GOLD}
            strokeWidth={2}
            dot={false}
            name="Gold"
          />
          <Line
            type="monotone"
            yAxisId="right"
            dataKey="sp500"
            stroke={COLORS.SP500}
            strokeWidth={2}
            dot={false}
            name="S&P 500"
          />
        </LineChart>
      </ResponsiveContainer>
    </div>
  );
};

export default BTCvsGOLDvsSP500;
