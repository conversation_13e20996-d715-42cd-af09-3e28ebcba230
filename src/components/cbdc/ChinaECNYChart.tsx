"use client";
import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  Bar<PERSON>hart,
} from "recharts";
import ChartOverlay from "../comman/ChartOverlay";

// Static data for China e-CNY transaction volume
const chinaData = [
   {
    year: "2021",
    volume: 0.09, // 0.09 Trillion
    displayValue: "¥0.09T",
    fullValue: 90000000000, // 0.09 trillion in actual value
  },
  {
    year: "2022",
    volume: 0.1, // 0.1 Trillion
    displayValue: "¥0.1T",
    fullValue: 100000000000, // 0.1 trillion in actual value
  },
  {
    year: "2023", 
    volume: 1.8, // 1.8 Trillion
    displayValue: "¥1.8T",
    fullValue: 1800000000000, // 1.8 trillion in actual value
  },
  {
    year: "2024",
    volume: 7.0, // 7 Trillion
    displayValue: "¥7T",
    fullValue: 7000000000000, // 7 trillion in actual value
  },
];

const ChinaECNYChart = () => {
  return (
    <div className="w-full bg-white p-6 rounded-lg shadow-sm">
      <div className="mb-6">
        <h3 className="text-xl font-bold text-gray-900 mb-2">
          e-CNY transaction volume quadruples from 2023
        </h3>
        <p className="text-sm text-gray-600 mb-1">
          Cumulative transaction volumes for select months (2021-2024)
        </p>
        {/* <p className="text-xs text-blue-600">
          Source: People&apos;s Bank of China
        </p> */}
      </div>
      
      <ChartOverlay>
        <div style={{ width: "100%", height: 350, color: 'black' }}>
          <ResponsiveContainer width="100%" height="100%">
            <BarChart
              data={chinaData}
              margin={{ top: 20, right: 30, left: 20, bottom: 20 }}
              barCategoryGap="30%"
            >
              <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
              <XAxis
                dataKey="year"
                tick={{ fontSize: 14, fill: "#374151" }}
                tickLine={false}
                axisLine={false}
              />
              <YAxis
                domain={[0, 8]}
                tick={{ fontSize: 12, fill: "#6b7280" }}
                tickLine={false}
                axisLine={false}
                tickFormatter={(value) => `¥${value}T`}
              />
              <Tooltip
                contentStyle={{
                  backgroundColor: "#ffffff",
                  border: "1px solid #e5e7eb",
                  borderRadius: "8px",
                  boxShadow: "0 4px 6px -1px rgba(0, 0, 0, 0.1)",
                }}
                formatter={(value) => [
                  chinaData.find(d => d.volume === value)?.displayValue || `¥${value}T`,
                  "Transaction Volume"
                ]}
                labelFormatter={(label) => `Year: ${label}`}
              />
              <Bar
                dataKey="volume"
                fill="#f87171"
                radius={[4, 4, 0, 0]}
                stroke="#ef4444"
                strokeWidth={1}
              />
            </BarChart>
          </ResponsiveContainer>
        </div>
      </ChartOverlay>
    </div>
  );
};

export default ChinaECNYChart;
