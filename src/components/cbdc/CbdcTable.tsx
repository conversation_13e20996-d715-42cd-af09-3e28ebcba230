"use client";

import Button from "@/components/comman/Button";
import { CBDCCurrency } from "@/lib/api.interface";
import { useCbdc, useCbdcTimeline } from "@/lib/state";
import { ConfigProvider, Table, TableProps } from "antd";
import { useState } from "react";
import ChartOverlay from "../comman/ChartOverlay";
import {
  Dialog,
  DialogContent,
} from "@/components/ui/dialog";
import Timeline from "@/components/cbdc/Timeline";
import { DialogTitle } from "@radix-ui/react-dialog";
import Loader from "@/components/comman/Loader";

export default function CbdcTable() {
  const cbdcQuery = useCbdc();
  const [isTimelineModalOpen, setIsTimelineModalOpen] = useState(false);
  const [selectedCbdcData, setSelectedCbdcData] = useState<CBDCCurrency | null>(null);
  const cbdcTimelineQuery = useCbdcTimeline('0', '10', selectedCbdcData?.tag || '');

  const cbdcColumns: TableProps<CBDCCurrency>["columns"] = [
    {
      title: "Digital currency",
      dataIndex: "digitalCurrency",
      key: "digitalCurrency",
      width: 200,
      align: "left",
      render: (text) => (
        <span className="font-medium text-text-primary">{text}</span>
      ),
    },
    {
      title: "Country / Region",
      dataIndex: "country",
      key: "country",
      align: "left",
      width: 150,
    },
    {
      title: "Central Bank(s)",
      dataIndex: "centralBank",
      key: "centralBank",
      align: "left",
      width: 250,
    },
    {
      title: "Announcement Year",
      dataIndex: "announcementYear",
      key: "announcementYear",
      align: "center",
      width: 150,
      sorter: (a, b) => a.announcementYear - b.announcementYear,
    },
    {
      title: "Status",
      dataIndex: "status",
      key: "status",
      width: 150,
      align: "left",
      filters: [
        { text: "Pilot", value: "Pilot" },
        { text: "Proof of concept", value: "Proof of concept" },
        { text: "Research", value: "Research" },
        { text: "Launched", value: "Launched" },
        { text: "Cancelled", value: "Cancelled" },
      ],
      onFilter: (value, record) => record.status.indexOf(value as string) === 0,
      render: (status: string) => {
        let boxColor = "bg-gray-300";
        if (status === "Pilot") boxColor = "bg-orange-200";
        if (status === "Proof of concept") boxColor = "bg-purple-300";
        if (status === "Research") boxColor = "bg-teal-200";
        if (status === "Launched") boxColor = "bg-green-300";
        if (status === "Cancelled") boxColor = "bg-red-300";

        return (
          <div className="flex items-center gap-2">
            <div className={`w-3 h-3 rounded ${boxColor}`}></div>
            <span className="font-semibold text-text-primary">{status}</span>
          </div>
        );
      },
    },
    {
      title: "Track Timeline",
      dataIndex: "trackTimeline",
      key: "trackTimeline",
      width: 150,
      align: "center",
      render: (_, record) => (
        <div className="flex justify-center">
          <Button
            variant="default"
            onClick={() => {
              setSelectedCbdcData(record);
              setIsTimelineModalOpen(true);
            }}
          >
            Track
          </Button>
        </div>
      ),
    },
  ];

  const processedCbdcData = cbdcQuery.data
    ? cbdcQuery.data
        .filter(
          (item) =>
            item.digitalCurrency &&
            item.country &&
            item.centralBank &&
            item.status
        )
        .map((item) => ({
          ...item,
          announcementYear: new Date(item.announcementYear).getFullYear(),
        }))
    : [];

    if (cbdcQuery.isLoading) {
      return <Loader />;
    }

    if (!cbdcQuery.isLoading && processedCbdcData.length === 0) {
      return <div>No data available</div>;
    }

  return (
      <section className="mr-4">
        <ChartOverlay>
          <ConfigProvider
            theme={{
              components: {
                Spin: {
                  colorPrimary: "#bbd955",
                },
                Table: {
                  fontFamily: "DM Sans",
                  colorPrimary: "#bbd955",
                },
              },
            }}
          >
            <Table
              columns={cbdcColumns}
              dataSource={processedCbdcData}
              rowKey="uid"
              loading={cbdcQuery.isLoading}
              scroll={{ x: "600px" }}
              pagination={{ pageSize: 15 }}
              className="crypto-table"
            />
          </ConfigProvider>
          <div className="pb-12" />
        </ChartOverlay>

        {/* Timeline Modal */}
        <Dialog open={isTimelineModalOpen} onOpenChange={(isOpen) => {
          setIsTimelineModalOpen(isOpen);
          // When the dialog closes, reset the selected data
          if (!isOpen) {
            setSelectedCbdcData(null);
          }
        }}>
          {/* Add DialogContent to wrap your modal's content */}
          <DialogContent className="p-0">
            <DialogTitle className="hidden">
            </DialogTitle>
            {cbdcTimelineQuery.isLoading && <Loader />}

            {!cbdcTimelineQuery.isLoading && cbdcTimelineQuery.data && (
              <Timeline
                data={cbdcTimelineQuery.data}
                title={`${selectedCbdcData?.digitalCurrency} Timeline`}
              />
            )}
            {cbdcTimelineQuery.isError && (
              <div className="text-red-500">
                Sorry, we couldn&apos;t load the timeline data.
              </div>
            )}
          </DialogContent>
        </Dialog>
      </section>
  );
}

