"use client";

import React, { useState } from "react";
import { Maximize2, X } from "lucide-react";
import {
  Dialog,
  DialogContent,
} from "@/components/ui/dialog";

interface FullscreenWrapperProps {
  children: React.ReactNode;
  title?: string;
  className?: string;
}

const FullscreenWrapper: React.FC<FullscreenWrapperProps> = ({
  children,
  title = "Chart",
  className = "",
}) => {
  const [isFullscreen, setIsFullscreen] = useState(false);

  const handleFullscreenToggle = () => {
    setIsFullscreen(!isFullscreen);
  };

  return (
    <>
      {/* Regular view with fullscreen button */}
      <div className={`relative ${className}`}>
        {/* Fullscreen button */}
        <button
          onClick={handleFullscreenToggle}
          className="absolute top-2 right-2 z-10 p-2 bg-gray-800/80 hover:bg-gray-700/80 text-white rounded-md transition-colors duration-200 backdrop-blur-sm"
          title="Open in fullscreen"
        >
          <Maximize2 size={16} />
        </button>
        
        {children}
      </div>

      {/* Fullscreen modal */}
      <Dialog open={isFullscreen} onOpenChange={setIsFullscreen}>
        <DialogContent className="max-w-[95vw] max-h-[95vh] w-full h-full p-0 bg-[#222831]">
          {/* Header with title and close button */}
          <div className="flex items-center justify-between p-4 border-b border-gray-600">
            <h2 className="text-lg font-medium text-white">{title}</h2>
            <button
              onClick={() => setIsFullscreen(false)}
              className="p-2 hover:bg-gray-700 text-white rounded-md transition-colors duration-200"
              title="Close fullscreen"
            >
              <X size={20} />
            </button>
          </div>
          
          {/* Content area */}
          <div className="flex-1 overflow-auto p-4">
            {children}
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default FullscreenWrapper;
