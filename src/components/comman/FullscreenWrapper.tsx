"use client";

import React, { useState, useRef, useEffect } from "react";
import { Maximize2, Minimize2 } from "lucide-react";

interface FullscreenWrapperProps {
  children: React.ReactNode;
  title?: string;
  className?: string;
}

const FullscreenWrapper: React.FC<FullscreenWrapperProps> = ({
  children,
  title = "Chart",
  className = "",
}) => {
  const [isFullscreen, setIsFullscreen] = useState(false);
  const containerRef = useRef<HTMLDivElement>(null);

  const enterFullscreen = async () => {
    if (containerRef.current) {
      try {
        if (containerRef.current.requestFullscreen) {
          await containerRef.current.requestFullscreen();
        } else if ((containerRef.current as any).webkitRequestFullscreen) {
          await (containerRef.current as any).webkitRequestFullscreen();
        } else if ((containerRef.current as any).msRequestFullscreen) {
          await (containerRef.current as any).msRequestFullscreen();
        }
        setIsFullscreen(true);
      } catch (error) {
        console.error("Error entering fullscreen:", error);
      }
    }
  };

  const exitFullscreen = async () => {
    try {
      if (document.exitFullscreen) {
        await document.exitFullscreen();
      } else if ((document as any).webkitExitFullscreen) {
        await (document as any).webkitExitFullscreen();
      } else if ((document as any).msExitFullscreen) {
        await (document as any).msExitFullscreen();
      }
      setIsFullscreen(false);
    } catch (error) {
      console.error("Error exiting fullscreen:", error);
    }
  };

  const handleFullscreenToggle = () => {
    if (isFullscreen) {
      exitFullscreen();
    } else {
      enterFullscreen();
    }
  };

  // Listen for fullscreen changes (e.g., when user presses ESC)
  useEffect(() => {
    const handleFullscreenChange = () => {
      const isCurrentlyFullscreen = !!(
        document.fullscreenElement ||
        (document as any).webkitFullscreenElement ||
        (document as any).msFullscreenElement
      );
      setIsFullscreen(isCurrentlyFullscreen);
    };

    document.addEventListener("fullscreenchange", handleFullscreenChange);
    document.addEventListener("webkitfullscreenchange", handleFullscreenChange);
    document.addEventListener("msfullscreenchange", handleFullscreenChange);

    return () => {
      document.removeEventListener("fullscreenchange", handleFullscreenChange);
      document.removeEventListener("webkitfullscreenchange", handleFullscreenChange);
      document.removeEventListener("msfullscreenchange", handleFullscreenChange);
    };
  }, []);

  return (
    <div
      ref={containerRef}
      className={`relative ${className}`}
      style={isFullscreen ? {
        width: '100vw',
        height: '100vh',
        backgroundColor: '#222831',
        padding: '2rem',
        display: 'flex',
        flexDirection: 'column',
        overflow: 'auto'
      } : {}}
    >
      {/* Fullscreen button */}
      <button
        onClick={handleFullscreenToggle}
        className="absolute top-4 right-4 z-50 p-3 bg-gray-800/90 hover:bg-gray-700/90 text-white rounded-md transition-colors duration-200 backdrop-blur-sm shadow-lg"
        title={isFullscreen ? "Exit fullscreen" : "Enter fullscreen"}
      >
        {isFullscreen ? <Minimize2 size={20} /> : <Maximize2 size={16} />}
      </button>

      {/* Title in fullscreen mode */}
      {isFullscreen && (
        <div className="mb-6 pt-16">
          <h2 className="text-3xl font-bold text-white">{title}</h2>
        </div>
      )}

      {/* Content wrapper */}
      <div
        className={isFullscreen ? 'flex-1 w-full h-full fullscreen-content' : ''}
        style={isFullscreen ? {
          minHeight: 'calc(100vh - 8rem)',
          display: 'flex',
          flexDirection: 'column'
        } : {}}
      >
        <div className={isFullscreen ? 'w-full h-full flex-1' : ''}>
          {children}
        </div>
      </div>
    </div>
  );
};

export default FullscreenWrapper;
