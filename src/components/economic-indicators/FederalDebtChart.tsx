"use client";

import React, { useMemo } from 'react';
import {
  <PERSON><PERSON>hart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
} from "recharts";
import { useFREDSeriesObservations } from '@/lib/state';
import { format, subYears } from 'date-fns';
import { formatChartDate } from '@/lib/utils';

import Loader from '../comman/Loader';

const FederalDebtChart: React.FC = () => {
  const endDate = format(new Date(), 'yyyy-MM-dd');
  const startDate = format(subYears(new Date(), 60), 'yyyy-MM-dd');

  // Fetch Federal Debt data
  const federalDebtQuery = useFREDSeriesObservations('GFDEBTN', startDate, endDate);

  // Fetch Bitcoin price data from FRED (CBBTCUSD)
  const btcQuery = useFREDSeriesObservations('CBBTCUSD', startDate, endDate);

  // Process and merge data for dual-axis chart
  const chartData = useMemo(() => {
    if (!federalDebtQuery.data?.observations) return [];

    const federalDebtData = federalDebtQuery.data.observations
      .filter(obs => obs.value !== '.' && obs.value !== '' && obs.value !== null)
      .map(obs => ({
        date: obs.date,
        federalDebt: parseFloat(obs.value),
      }));

    // Create Bitcoin data map (may be empty if no data)
    const btcMap = new Map();
    if (btcQuery.data?.observations) {
      btcQuery.data.observations
        .filter(obs => obs.value !== '.' && obs.value !== '' && obs.value !== null)
        .forEach(obs => {
          btcMap.set(obs.date, parseFloat(obs.value));
        });
    }

    // Merge data by date - include all Federal Debt data, Bitcoin where available
    const mergedData = federalDebtData
      .map(federalDebtItem => ({
        date: federalDebtItem.date,
        federalDebt: federalDebtItem.federalDebt,
        btc: btcMap.get(federalDebtItem.date) || null, // null if no Bitcoin data for this date
        formattedDate: formatChartDate(federalDebtItem.date, { showDay: true }),
      }))
      .sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());

    return mergedData;
  }, [federalDebtQuery.data, btcQuery.data]);

  const formatDebtValue = (value: number) => {
    if (value >= 1000000) {
      return `$${(value / 1000000).toFixed(1)}T`;
    } else if (value >= 1000) {
      return `$${(value / 1000).toFixed(0)}B`;
    }
    return `$${value.toFixed(0)}M`;
  };

  const formatBTCValue = (value: number) => {
    if (value >= 1000) {
      return `$${(value / 1000).toFixed(1)}K`;
    }
    return `$${value.toLocaleString()}`;
  };

  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-gray-800 p-3 border border-gray-600 rounded-lg shadow-lg">
          <p className="text-white font-medium">{`Date: ${formatChartDate(label, { showDay: true, showMonth: true, showYear: true })}`}</p>
          {payload.map((entry: any, index: number) => (
            <p key={index} style={{ color: entry.color }} className="text-sm">
              {entry.name}: {entry.name === 'Federal Debt' ? formatDebtValue(entry.value) : formatBTCValue(entry.value)}
            </p>
          ))}
        </div>
      );
    }
    return null;
  };

  if (federalDebtQuery.isLoading || btcQuery.isLoading) {
    return (
      <div className="w-full space-y-8 p-6">
        <div className="flex items-center justify-center" style={{ height: 500 }}>
          <Loader />
        </div>
      </div>
    );
  }

  if (!chartData || chartData.length === 0) {
    return (
      <div className="w-full space-y-8 p-6">
        <div className="flex items-center justify-center flex-col space-y-4" style={{ height: 500 }}>
          <p className="text-white text-lg">Economic Data Coming Soon</p>
          <p className="text-gray-400 text-sm text-center max-w-md">
            The FRED API integration is ready. Backend endpoints are being deployed to fetch economic indicators data.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full space-y-8 p-6">
      <div className="space-y-8">
        <div className="w-full space-y-4">
          <h2 className="text-2xl font-bold text-white">Federal Debt vs Bitcoin Price</h2>
          <div className="w-full">
            <div style={{ width: "100%", height: 500 }}>
              <ResponsiveContainer width="100%" height="100%">
                <LineChart
                  data={chartData}
                  margin={{ top: 20, right: 80, left: 20, bottom: 20 }}
                >
                  <CartesianGrid stroke="#5d5e5f" strokeDasharray="3 3" />
                  <XAxis
                    dataKey="date"
                    tick={{ fill: "#fff", fontSize: 12 }}
                    tickFormatter={(value) => {
                      const date = new Date(value);
                      return date.getFullYear().toString();
                    }}
                    tickLine={false}
                    axisLine={false}
                    interval="preserveStartEnd"
                  />
                  <YAxis
                    yAxisId="federalDebt"
                    orientation="left"
                    tick={{ fill: "#fff", fontSize: 12 }}
                    tickLine={false}
                    axisLine={false}
                    tickFormatter={formatDebtValue}
                    label={{
                      value: "Federal Debt (Millions USD)",
                      angle: -90,
                      position: "insideLeft",
                      offset: 10,
                      style: { fill: "#E74C3C", fontSize: 14, fontWeight: 500 },
                    }}
                  />
                  <YAxis
                    yAxisId="btc"
                    orientation="right"
                    tick={{ fill: "#fff", fontSize: 12 }}
                    tickLine={false}
                    axisLine={false}
                    tickFormatter={formatBTCValue}
                    label={{
                      value: "Bitcoin Price (USD)",
                      angle: 90,
                      position: "insideRight",
                      offset: -5,
                      style: { fill: "#F7931A", fontSize: 14, fontWeight: 500 },
                    }}
                  />
                  <Tooltip content={<CustomTooltip />} />
                  {/* Federal Debt Line */}
                  <Line
                    yAxisId="federalDebt"
                    type="monotone"
                    dataKey="federalDebt"
                    stroke="#E74C3C"
                    strokeWidth={2}
                    dot={false}
                    activeDot={{ r: 4, fill: "#E74C3C" }}
                    name="Federal Debt"
                  />
                  {/* Bitcoin Line */}
                  <Line
                    yAxisId="btc"
                    type="monotone"
                    dataKey="btc"
                    stroke="#F7931A"
                    strokeWidth={2}
                    dot={false}
                    activeDot={{ r: 4, fill: "#F7931A" }}
                    name="Bitcoin Price"
                    connectNulls={false}
                  />
                </LineChart>
              </ResponsiveContainer>
            </div>
          </div>
        </div>

        {/* Information about Federal Debt vs BTC */}
        <div className="bg-gray-800 p-4 rounded-lg">
          <h3 className="text-white text-lg font-semibold mb-2">Federal Debt and Bitcoin</h3>
          <p className="text-gray-400 text-sm">
            Rising federal debt often leads to concerns about currency debasement and fiscal sustainability.
            Bitcoin proponents view it as a hedge against unlimited government spending and debt monetization.
            As debt levels increase, some investors turn to Bitcoin as a store of value independent of
            government fiscal policy.
          </p>
        </div>
      </div>
    </div>
  );
};

export default FederalDebtChart;
