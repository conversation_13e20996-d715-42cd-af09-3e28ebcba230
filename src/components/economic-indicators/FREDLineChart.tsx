"use client";

import React from 'react';
import {
  <PERSON><PERSON>hart,
  Line,
  XAxis,
  <PERSON>Axis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
} from "recharts";
import { FREDObservation } from '@/lib/api.interface';
import { formatChartDate } from '@/lib/utils';

import Loader from '../comman/Loader';

export interface FREDChartDataPoint {
  date: string;
  value: number;
  formattedDate: string;
}

interface FREDLineChartProps {
  data: FREDObservation[];
  title: string;
  isLoading?: boolean;
  color?: string;
  yAxisLabel?: string;
  formatValue?: (value: number) => string;
  height?: number;
}

const COLORS = {
  PRIMARY: "#bbd955",
  GRID: "#e0e0e0",
  TEXT: "#3f3f3f",
  WHITE: "#ffffff",
};

const FREDLineChart: React.FC<FREDLineChartProps> = ({
  data,
  title,
  isLoading = false,
  color = COLORS.PRIMARY,
  yAxisLabel,
  formatValue,
  height = 400,
}) => {
  // Process FRED data for chart
  const chartData: FREDChartDataPoint[] = React.useMemo(() => {
    if (!data || data.length === 0) return [];

    return data
      .filter(observation => observation.value !== '.' && observation.value !== '' && observation.value !== null)
      .map(observation => ({
        date: observation.date,
        value: parseFloat(observation.value),
        formattedDate: formatChartDate(observation.date, { showDay: true }),
      }))
      .sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());
  }, [data]);

  const defaultFormatValue = (value: number) => {
    if (value >= 1000000000) {
      return `${(value / 1000000000).toFixed(1)}B`;
    } else if (value >= 1000000) {
      return `${(value / 1000000).toFixed(1)}M`;
    } else if (value >= 1000) {
      return `${(value / 1000).toFixed(1)}K`;
    }
    return value.toLocaleString();
  };

  const valueFormatter = formatValue || defaultFormatValue;

  if (isLoading) {
    return (
      <div className="w-full">
        <div className="flex items-center justify-center" style={{ height }}>
          <Loader />
        </div>
      </div>
    );
  }

  if (!chartData || chartData.length === 0) {
    return (
      <div className="w-full">
        <div className="flex items-center justify-center flex-col space-y-4" style={{ height }}>
          <p className="text-white text-lg">Economic Data Coming Soon</p>
          <p className="text-gray-400 text-sm text-center max-w-md">
            The FRED API integration is ready. Backend endpoints are being deployed to fetch economic indicators data.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full space-y-4">
      <h2 className="text-2xl font-bold text-white">{title}</h2>
      <div className="w-full">
        <div style={{ width: "100%", height }}>
          <ResponsiveContainer width="100%" height="100%">
            <LineChart
              data={chartData}
              margin={{ top: 20, right: 30, left: 20, bottom: 20 }}
            >
              <CartesianGrid stroke={COLORS.GRID} strokeDasharray="3 3" />
              <XAxis
                dataKey="date"
                tick={{ fill: COLORS.TEXT, fontSize: 12 }}
                tickFormatter={(value) => {
                  const date = new Date(value);
                  return date.getFullYear().toString();
                }}
                tickLine={false}
                axisLine={false}
                interval="preserveStartEnd"
              />
              <YAxis
                orientation="right"
                tick={{ fill: COLORS.TEXT, fontSize: 12 }}
                tickLine={false}
                axisLine={false}
                tickFormatter={valueFormatter}
                label={yAxisLabel ? {
                  value: yAxisLabel,
                  angle: 90,
                  position: "insideRight",
                  offset: -5,
                  style: { fill: color, fontSize: 14, fontWeight: 500 },
                } : undefined}
              />
              <Tooltip
                formatter={(val: number) => [valueFormatter(val), title]}
                labelFormatter={(label) => `Date: ${new Date(label).toLocaleDateString()}`}
                contentStyle={{
                  backgroundColor: "#1a1a1a",
                  border: "1px solid #333",
                  borderRadius: "8px",
                  color: COLORS.WHITE,
                }}
              />
              <Line
                type="monotone"
                dataKey="value"
                stroke={color}
                strokeWidth={2}
                dot={false}
                activeDot={{ r: 4, fill: color }}
              />
            </LineChart>
          </ResponsiveContainer>
        </div>
      </div>
    </div>
  );
};

export default FREDLineChart;
