"use client";

import React, { useMemo } from 'react';
import {
  <PERSON><PERSON><PERSON>,
  Line,
  XAxis,
  <PERSON>Axis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
} from "recharts";
import { useFREDSeriesObservations } from '@/lib/state';
import { format, subYears } from 'date-fns';
import { formatChartDate } from '@/lib/utils';

import Loader from '../comman/Loader';
import FullscreenWrapper from '../comman/FullscreenWrapper';

const UnemploymentRateChart: React.FC = () => {
  const endDate = format(new Date(), 'yyyy-MM-dd');
  const startDate = format(subYears(new Date(), 40), 'yyyy-MM-dd');

  // Fetch Unemployment Rate data
  const unemploymentQuery = useFREDSeriesObservations('UNRATE', startDate, endDate);

  // Fetch Bitcoin price data from FRED (CBBTCUSD)
  const btcQuery = useFREDSeriesObservations('CBBTCUSD', startDate, endDate);

  // Process and merge data for dual-axis chart
  const chartData = useMemo(() => {
    if (!unemploymentQuery.data?.observations) return [];

    const unemploymentData = unemploymentQuery.data.observations
      .filter(obs => obs.value !== '.' && obs.value !== '' && obs.value !== null)
      .map(obs => ({
        date: obs.date,
        unemployment: parseFloat(obs.value),
      }));

    // Create Bitcoin data map (may be empty if no data)
    const btcMap = new Map();
    if (btcQuery.data?.observations) {
      btcQuery.data.observations
        .filter(obs => obs.value !== '.' && obs.value !== '' && obs.value !== null)
        .forEach(obs => {
          btcMap.set(obs.date, parseFloat(obs.value));
        });
    }

    // Merge data by date - include all Unemployment data, Bitcoin where available
    const mergedData = unemploymentData
      .map(unemploymentItem => ({
        date: unemploymentItem.date,
        unemployment: unemploymentItem.unemployment,
        btc: btcMap.get(unemploymentItem.date) || null, // null if no Bitcoin data for this date
        formattedDate: formatChartDate(unemploymentItem.date, { showDay: true }),
      }))
      .sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());

    return mergedData;
  }, [unemploymentQuery.data, btcQuery.data]);

  const formatUnemploymentValue = (value: number) => {
    return `${value.toFixed(1)}%`;
  };

  const formatBTCValue = (value: number) => {
    if (value >= 1000) {
      return `$${(value / 1000).toFixed(1)}K`;
    }
    return `$${value.toLocaleString()}`;
  };

  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-gray-800 p-3 border border-gray-600 rounded-lg shadow-lg">
          <p className="text-white font-medium">{`Date: ${formatChartDate(label, { showDay: true, showMonth: true, showYear: true })}`}</p>
          {payload.map((entry: any, index: number) => (
            <p key={index} style={{ color: entry.color }} className="text-sm">
              {entry.name}: {entry.name === 'Unemployment Rate' ? formatUnemploymentValue(entry.value) : formatBTCValue(entry.value)}
            </p>
          ))}
        </div>
      );
    }
    return null;
  };

  if (unemploymentQuery.isLoading || btcQuery.isLoading) {
    return (
      <div className="w-full space-y-8 p-6">
        <div className="flex items-center justify-center" style={{ height: 500 }}>
          <Loader />
        </div>
      </div>
    );
  }

  if (!chartData || chartData.length === 0) {
    return (
      <div className="w-full space-y-8 p-6">
        <div className="flex items-center justify-center flex-col space-y-4" style={{ height: 500 }}>
          <p className="text-white text-lg">Economic Data Coming Soon</p>
          <p className="text-gray-400 text-sm text-center max-w-md">
            The FRED API integration is ready. Backend endpoints are being deployed to fetch economic indicators data.
          </p>
        </div>
      </div>
    );
  }

  return (
    <FullscreenWrapper title="Unemployment Rate vs Bitcoin Price">
      <div className="w-full space-y-8 p-6">
      <div className="space-y-8">
        <div className="w-full space-y-4">
          <h2 className="text-2xl font-bold text-white">Unemployment Rate vs Bitcoin Price</h2>
          <div className="w-full">
            <div style={{ width: "100%", height: 500 }}>
              <ResponsiveContainer width="100%" height="100%">
                <LineChart
                  data={chartData}
                  margin={{ top: 20, right: 80, left: 20, bottom: 20 }}
                >
                  <CartesianGrid stroke="#5d5e5f" strokeDasharray="3 3" />
                  <XAxis
                    dataKey="date"
                    tick={{ fill: "#fff", fontSize: 12 }}
                    tickFormatter={(value) => {
                      const date = new Date(value);
                      return date.getFullYear().toString();
                    }}
                    tickLine={false}
                    axisLine={false}
                    interval="preserveStartEnd"
                  />
                  <YAxis
                    yAxisId="unemployment"
                    orientation="left"
                    tick={{ fill: "#fff", fontSize: 12 }}
                    tickLine={false}
                    axisLine={false}
                    tickFormatter={formatUnemploymentValue}
                    label={{
                      value: "Unemployment Rate (%)",
                      angle: -90,
                      position: "insideLeft",
                      offset: 10,
                      style: { fill: "#F39C12", fontSize: 14, fontWeight: 500 },
                    }}
                  />
                  <YAxis
                    yAxisId="btc"
                    orientation="right"
                    tick={{ fill: "#fff", fontSize: 12 }}
                    tickLine={false}
                    axisLine={false}
                    tickFormatter={formatBTCValue}
                    label={{
                      value: "Bitcoin Price (USD)",
                      angle: 90,
                      position: "insideRight",
                      offset: -5,
                      style: { fill: "#F7931A", fontSize: 14, fontWeight: 500 },
                    }}
                  />
                  <Tooltip content={<CustomTooltip />} />
                  {/* Unemployment Rate Line */}
                  <Line
                    yAxisId="unemployment"
                    type="monotone"
                    dataKey="unemployment"
                    stroke="#F39C12"
                    strokeWidth={2}
                    dot={false}
                    activeDot={{ r: 4, fill: "#F39C12" }}
                    name="Unemployment Rate"
                  />
                  {/* Bitcoin Line */}
                  <Line
                    yAxisId="btc"
                    type="monotone"
                    dataKey="btc"
                    stroke="#F7931A"
                    strokeWidth={2}
                    dot={false}
                    activeDot={{ r: 4, fill: "#F7931A" }}
                    name="Bitcoin Price"
                    connectNulls={false}
                  />
                </LineChart>
              </ResponsiveContainer>
            </div>
          </div>
        </div>

        {/* Information about Unemployment vs BTC */}
        <div className="bg-gray-800 p-4 rounded-lg">
          <h3 className="text-white text-lg font-semibold mb-2">Unemployment Rate and Economic Impact</h3>
          <p className="text-gray-400 text-sm">
            The unemployment rate is a key indicator of economic health. High unemployment often leads to
            expansionary monetary policy (money printing) to stimulate the economy. This can drive inflation
            and currency debasement, making Bitcoin attractive as an alternative store of value. Conversely,
            low unemployment may lead to tighter monetary policy.
          </p>
        </div>
      </div>
    </div>
    </FullscreenWrapper>
  );
};

export default UnemploymentRateChart;
