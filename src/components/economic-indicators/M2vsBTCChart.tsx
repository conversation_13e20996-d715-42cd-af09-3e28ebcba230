"use client";

import React, { useMemo } from 'react';
import {
  <PERSON><PERSON><PERSON>,
  Line,
  XAxis,
  <PERSON>Axis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
} from "recharts";
import { useFREDSeriesObservations } from '@/lib/state';
import { format, subYears } from 'date-fns';
import { formatChartDate } from '@/lib/utils';

import Loader from '../comman/Loader';
import FullscreenWrapper from '../comman/FullscreenWrapper';

const M2vsBTCChart: React.FC = () => {
  const endDate = format(new Date(), 'yyyy-MM-dd');
  const startDate = format(subYears(new Date(), 40), 'yyyy-MM-dd');

  // Fetch M2 Money Supply data
  const m2Query = useFREDSeriesObservations('M2SL', startDate, endDate);

  // Fetch Bitcoin price data from FRED (CBBTCUSD)
  const btcQuery = useFREDSeriesObservations('CBBTCUSD', startDate, endDate);

  // Process and merge data for dual-axis chart
  const chartData = useMemo(() => {
    if (!m2Query.data?.observations) return [];

    const m2Data = m2Query.data.observations
      .filter(obs => obs.value !== '.' && obs.value !== '' && obs.value !== null)
      .map(obs => ({
        date: obs.date,
        m2: parseFloat(obs.value),
      }));

    // Create Bitcoin data map (may be empty if no data)
    const btcMap = new Map();
    if (btcQuery.data?.observations) {
      btcQuery.data.observations
        .filter(obs => obs.value !== '.' && obs.value !== '' && obs.value !== null)
        .forEach(obs => {
          btcMap.set(obs.date, parseFloat(obs.value));
        });
    }

    // Merge data by date - include all M2 data, Bitcoin where available
    const mergedData = m2Data
      .map(m2Item => ({
        date: m2Item.date,
        m2: m2Item.m2,
        btc: btcMap.get(m2Item.date) || null, // null if no Bitcoin data for this date
        formattedDate: formatChartDate(m2Item.date, { showDay: true }),
      }))
      .sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());

    return mergedData;
  }, [m2Query.data, btcQuery.data]);

  const formatM2Value = (value: number) => {
    if (value >= 1000) {
      return `$${(value / 1000).toFixed(1)}T`;
    }
    return `$${value.toFixed(0)}B`;
  };

  const formatBTCValue = (value: number) => {
    if (value >= 1000) {
      return `$${(value / 1000).toFixed(1)}K`;
    }
    return `$${value.toLocaleString()}`;
  };

  const isLoading = m2Query.isLoading;

  if (isLoading) {
    return (
      <div className="w-full">
        <div className="flex items-center justify-center" style={{ height: 500 }}>
          <Loader />
        </div>
      </div>
    );
  }

  if (!chartData || chartData.length === 0) {
    return (
      <div className="w-full">
        <div className="flex items-center justify-center flex-col space-y-4" style={{ height: 500 }}>
          <p className="text-white text-lg">Economic Data Coming Soon</p>
          <p className="text-gray-400 text-sm text-center max-w-md">
            The FRED API integration is ready. Backend endpoints are being deployed to fetch economic indicators data.
          </p>
        </div>
      </div>
    );
  }

  return (
    <FullscreenWrapper title="M2 Money Supply vs Bitcoin Price">
      <div className="w-full space-y-8 p-6">
      <div className="space-y-8">
        <div className="w-full space-y-4">
          <h2 className="text-2xl font-bold text-white">M2 Money Supply vs Bitcoin Price</h2>
          <div className="w-full">
            <div style={{ width: "100%", height: 500 }}>
              <ResponsiveContainer width="100%" height="100%">
                <LineChart
                  data={chartData}
                  margin={{ top: 20, right: 80, left: 20, bottom: 20 }}
                >
                  <CartesianGrid stroke="#5d5e5f" strokeDasharray="3 3" />
                  <XAxis
                    dataKey="date"
                    tick={{ fill: "#fff", fontSize: 12 }}
                    tickFormatter={(value) => {
                      const date = new Date(value);
                      return date.getFullYear().toString();
                    }}
                    tickLine={false}
                    axisLine={false}
                    interval="preserveStartEnd"
                  />
                  {/* Left Y-axis for M2 */}
                  <YAxis
                    yAxisId="m2"
                    orientation="left"
                    tick={{ fill: "#FF6B6B", fontSize: 12 }}
                    tickLine={false}
                    axisLine={false}
                    tickFormatter={formatM2Value}
                    label={{
                      value: "M2 Money Supply (Billions)",
                      angle: -90,
                      position: "insideLeft",
                      style: { fill: "#FF6B6B", fontSize: 14, fontWeight: 500 },
                    }}
                  />
                  {/* Right Y-axis for Bitcoin */}
                  <YAxis
                    yAxisId="btc"
                    orientation="right"
                    tick={{ fill: "#F7931A", fontSize: 12 }}
                    tickLine={false}
                    axisLine={false}
                    tickFormatter={formatBTCValue}
                    label={{
                      value: "Bitcoin Price (USD)",
                      angle: 90,
                      position: "insideRight",
                      offset: -5,
                      style: { fill: "#F7931A", fontSize: 14, fontWeight: 500 },
                    }}
                  />
                  <Tooltip
                    formatter={(val: number, name: string) => {
                      if (name === 'M2 Money Supply') {
                        return [formatM2Value(val), name];
                      } else if (name === 'Bitcoin Price') {
                        return [formatBTCValue(val), name];
                      }
                      return [val, name];
                    }}
                    labelFormatter={(label) => `Date: ${formatChartDate(label, { showDay: true, showMonth: true, showYear: true })}`}
                    contentStyle={{
                      backgroundColor: "#1a1a1a",
                      border: "1px solid #333",
                      borderRadius: "8px",
                      color: "#ffffff",
                    }}
                  />
                  {/* M2 Line */}
                  <Line
                    yAxisId="m2"
                    type="monotone"
                    dataKey="m2"
                    stroke="#FF6B6B"
                    strokeWidth={2}
                    dot={false}
                    activeDot={{ r: 4, fill: "#FF6B6B" }}
                    name="M2 Money Supply"
                  />
                  {/* Bitcoin Line */}
                  <Line
                    yAxisId="btc"
                    type="monotone"
                    dataKey="btc"
                    stroke="#F7931A"
                    strokeWidth={2}
                    dot={false}
                    activeDot={{ r: 4, fill: "#F7931A" }}
                    name="Bitcoin Price"
                    connectNulls={false}
                  />
                </LineChart>
              </ResponsiveContainer>
            </div>
          </div>
        </div>

        {/* Information about M2 vs BTC */}
        <div className="bg-gray-800 p-4 rounded-lg">
          <h3 className="text-white text-lg font-semibold mb-2">M2 Money Supply vs Bitcoin Relationship</h3>
          <p className="text-gray-400 text-sm">
            This chart shows the relationship between M2 money supply expansion and Bitcoin price over time.
            As M2 increases (money printing), Bitcoin often serves as a hedge against currency debasement.
            The dual-axis view allows you to see how both metrics have evolved together.
          </p>
        </div>
      </div>
    </div>
    </FullscreenWrapper>
  );
};

export default M2vsBTCChart;
