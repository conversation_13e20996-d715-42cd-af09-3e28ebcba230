"use client";
import { cn } from "@/lib/utils";
import { useState } from "react";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { ChevronDown, Minus } from "lucide-react";
import { Wallet } from "lucide-react";

import { FILTER_LIST } from "@/components/mofse-advance/constant";
import AssetContent from "@/components/cbdc/AssetContent";
import Header from "@/components/comman/Header";
import Footer from "@/components/comman/Footer";
// import ProtectedRoute from "@/components/comman/ProtectedRoute";

const CBDCWrapper = () => {
  const [filters] = useState([
    {
      title: "CBDC Tracker",
      icon: <Wallet />,
      options: [
        {
          label: "CBDC Tracker",
          value: FILTER_LIST.CBDC,
        },
      ],
    },
     {
      title: "Circulation",
      icon: <Wallet />,
      options: [
        {
          label: "China",
          value: FILTER_LIST.CHINA,
        },
        {
          label: "India",
          value: FILTER_LIST.INDIA,
        },
      ],
    },
  ]);
  const [activeFilter, setActiveFilter] = useState<string>(FILTER_LIST.CBDC);
  return (
    <div style={{ backgroundColor: '#222831', minHeight: '100vh' }}>
      <Header isDarkTheme={true} />
      <div className="py-0 text-white">
        <div className="flex tablet:flex-row flex-col">
          <div className="w-full tablet:w-96 tablet:h-screen tablet:overflow-y-auto tablet:border-r border-gray-600 py-4 px-4 ">
            {filters.length > 0 && (
              <Accordion
                type="multiple"
                className="w-full"
                defaultValue={filters.map((filter) => filter.title)}
              >
                {" "}
                {/* Use "multiple" if you want more than one open at a time */}
                {filters.map((filter) => (
                  <AccordionItem
                    value={filter.title}
                    key={filter.title}
                    className="border-none"
                  >
                    <AccordionTrigger className="group flex items-center justify-between w-full py-3 pl-2 pr-4 text-sm font-medium bg-white text-gray-800  hover:no-underline [&>svg]:h-5 [&>svg]:w-5 [&>svg]:shrink-0 [&>svg]:text-white [&>svg]:transition-transform [&>svg]:duration-200 [&[data-state=open]>svg]:rotate-90 ">
                      <span className="flex items-center justify-between gap-3 w-full cursor-pointer">
                        {" "}
                        {/* Wrapper for icon and title */}
                        <div className="flex items-center gap-3">
                          {filter.icon}
                          {filter.title}
                        </div>
                        <ChevronDown />
                      </span>
                    </AccordionTrigger>
                    <AccordionContent className="pb-1 pt-1">
                      <ul className="space-y-1 ml-2">
                        {filter.options.map((option) => (
                          <li
                             className={cn(
                            "flex items-center gap-2 cursor-pointer px-4 rounded",
                            "text-gray-300 hover:bg-bg-primary hover:text-black",
                            activeFilter === option.value
                              ? "bg-bg-primary text-black"
                              : ""
                          )}
                            onClick={() => setActiveFilter(option.value)}
                            key={option.value}
                          >
                            <Minus className="mr-2" /> {option.label}
                          </li>
                        ))}
                      </ul>
                    </AccordionContent>
                  </AccordionItem>
                ))}
              </Accordion>
            )}
          </div>

          <div className="w-full">
            <AssetContent filter={activeFilter} />
          </div>
        </div>
      </div>
      <Footer />
    </div>
  );
};

export default CBDCWrapper;
