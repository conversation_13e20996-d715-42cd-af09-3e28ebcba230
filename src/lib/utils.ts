import { clsx, type ClassValue } from "clsx";
import { twMerge } from "tailwind-merge";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export const shortenText = (text = "", maxLength = 40) =>
  text.length > maxLength ? `${text.slice(0, maxLength)}...` : text;

export function formatPriceInRoman(num: number | null | undefined): string {
  if (num === null || num === undefined || isNaN(num)) {
    return "-";
  }

  const suffixes: string[] = ["", "K", "M", "B", "T"];
  let i = 0;

  while (num >= 1000 && i < suffixes.length - 1) {
    num /= 1000;
    i++;
  }

  return `${num.toFixed(2)}${suffixes[i]}`;
}

export function formatPriceInRomanFull(num: number | null | undefined): string {
  if (num === null || num === undefined || isNaN(num)) {
    return "-";
  }

  const suffixes: string[] = ["", "K", "Million", "Billion", "Trillion"];
  let i = 0;

  while (num >= 1000 && i < suffixes.length - 1) {
    num /= 1000;
    i++;
  }

  return `${num.toFixed(2)} ${suffixes[i]}`;
}

export function formatLargeCurrency(str: string): string {
  // Remove dollar signs and commas
  const cleaned = str.replace(/[$,]/g, "");
  const num = parseFloat(cleaned);

  if (isNaN(num)) return "Invalid number";

  const absNum = Math.abs(num);

  if (absNum >= 1e12) {
    return (num / 1e12).toFixed(2) + "T";
  } else if (absNum >= 1e9) {
    return (num / 1e9).toFixed(2) + "B";
  } else if (absNum >= 1e6) {
    return (num / 1e6).toFixed(2) + "M";
  } else if (absNum >= 1e3) {
    return (num / 1e3).toFixed(2) + "K";
  } else {
    return num.toString();
  }
}

export function formatDateTimeInYearMonthDay(
  date: string,
  showDay = true,
  showMonth = true,
  showYear = true
): string {
  if (date === null || date === undefined) {
    return "Invalid date";
  }

  const dateObj = new Date(date);

  if (isNaN(dateObj.getTime())) {
    return "Invalid date";
  }

  // Array for month names
  const months = [
    "Jan",
    "Feb",
    "Mar",
    "Apr",
    "May",
    "Jun",
    "Jul",
    "Aug",
    "Sep",
    "Oct",
    "Nov",
    "Dec",
  ];

  // Get date components
  const day = dateObj.getDate();
  const month = months[dateObj.getMonth()];
  const year = dateObj.getFullYear();

  // Return formatted string
  return `${showDay ? day : ""} ${showMonth ? month : ""} ${
    showYear ? year : ""
  }`;
}

export function formatDateTimeInYearMonth(date: string | Date): string {
  const d = new Date(date);
  return d.toLocaleDateString("en-US", {
    year: "numeric",
    month: "long",
  });
}

export function getInitials(name: string): string {
  const lastSpaceIdx = name.lastIndexOf(" ");
  let initials = name.charAt(0);
  if (lastSpaceIdx > 0 && name.length > lastSpaceIdx + 1) {
    initials += name.charAt(lastSpaceIdx + 1);
  }
  return initials;
}

export const convertToSpaces = (name: string) => {
  if (!name) return name;
  return name.replace(/-/g, " ");
};

export const convertToKebabCase = (name: string) => {
  return name
    .replace(/([a-z])([A-Z])/g, "$1-$2")
    .replace(/[\s_]+/g, "-")
    .toLowerCase();
};

export function formatPrice(num: number): string {
  if (num >= 1) {
    // For numbers >= 1, show 2 decimal places
    return num.toLocaleString("en-US", {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    });
  } else if (num >= 0.1) {
    // For numbers between 0.1 and 1, show 4 decimal places
    return num.toLocaleString("en-US", {
      minimumFractionDigits: 4,
      maximumFractionDigits: 4,
    });
  } else if (num > 0) {
    // For very small positive numbers, show up to 10 decimal places
    return num.toFixed(10).replace(/\.?0+$/, ""); // Remove trailing zeros
  } else {
    // Handle zero explicitly
    return "0.00";
  }
}

export function formatEpochDateTime(
  epoch: number,
  showDay = true,
  showMonth = true,
  showYear = true,
  showHours = true,
  showMinutes = true,
  showSeconds = true
): string {
  if (epoch === null || epoch === undefined) {
    return "Invalid date";
  }
  // Create a new Date object from epoch (multiply by 1000 if epoch is in seconds)
  const date = new Date(epoch * 1000);

  // Array for month names
  const months = [
    "Jan",
    "Feb",
    "Mar",
    "Apr",
    "May",
    "Jun",
    "Jul",
    "Aug",
    "Sep",
    "Oct",
    "Nov",
    "Dec",
  ];

  // Get date components
  const day = date.getDate();
  const month = months[date.getMonth()];

  const year = date.getFullYear();

  // Get time components with padding
  const hours = String(date.getHours()).padStart(2, "0");
  const minutes = String(date.getMinutes()).padStart(2, "0");
  const seconds = String(date.getSeconds()).padStart(2, "0");

  // Return formatted string
  return `${showDay ? day : ""} ${showMonth ? month : ""} ${
    showYear ? year : ""
  } ${showHours ? hours : ""}${showMinutes ? `:${minutes}` : ""}${
    showSeconds ? `:${seconds}` : ""
  }`;
}

export const formatDateTime = (epoch: number): string => {
  const date = new Date(epoch * 1000);
  const months = [
    "jan",
    "feb",
    "mar",
    "apr",
    "may",
    "jun",
    "jul",
    "aug",
    "sep",
    "oct",
    "nov",
    "dec",
  ];

  const hours = String(date.getHours()).padStart(2, "0");
  const minutes = String(date.getMinutes()).padStart(2, "0");

  return `${date.getDate()} ${months[date.getMonth()]} ${hours}:${minutes}`;
};

export function timeAgo(timestamp: number): string {
  const now = Date.now();
  const diffMs = now - timestamp;

  if (diffMs < 0) return "0 sec";

  const seconds = Math.floor(diffMs / 1000);
  const minutes = Math.floor(diffMs / (1000 * 60));
  const hours = Math.floor(diffMs / (1000 * 60 * 60));

  let value: number;
  let unit: string;

  if (seconds < 60) {
    value = seconds;
    unit = "sec";
  } else if (minutes < 60) {
    value = minutes;
    unit = "min";
  } else if (hours < 24) {
    value = hours;
    unit = "hr";
  } else {
    value = 0;
    unit = "sec";
  }

  // const plural = value !== 1 ? "s" : "";

  return `${value} ${unit}`;
}

export function formatChartDate(
  input: string,
  options?: {
    showDay?: boolean;
    showMonth?: boolean;
    showYear?: boolean;
  }
): string {
  const date = new Date(input);

  // By default, show month and year, but not the day.
  const { showDay = false, showMonth = true, showYear = true } = options || {};

  const formatOptions: Intl.DateTimeFormatOptions = {};

  if (showYear) {
    formatOptions.year = "numeric";
  }
  if (showMonth) {
    formatOptions.month = "short";
  }
  if (showDay) {
    formatOptions.day = "2-digit";
  }

  // If no options are true, return an empty string to avoid errors.
  if (!showDay && !showMonth && !showYear) {
    console.warn("formatChartDate called with no options enabled.");
    return '';
  }

  return date.toLocaleDateString("en-GB", formatOptions);
}

export function formatUnixToChartDate(
  timestamp: number,
  options?: { showDate?: boolean; showTime?: boolean }
): string {
  const { showDate = true, showTime = true } = options || {};
  const date = new Date(timestamp * 1000); // Convert from seconds to milliseconds

  const day = String(date.getDate()).padStart(2, "0");
  const month = date.toLocaleString("en-GB", { month: "short" }); // e.g., "May"
  const year = date.getFullYear();

  const datePart = showDate ? `${day} ${month} ${year}` : `${month} ${year}`;

  const timePart = showTime
    ? `${String(date.getHours()).padStart(2, "0")}:${String(
        date.getMinutes()
      ).padStart(2, "0")}`
    : "";

  return [datePart, timePart].filter(Boolean).join(" ");
}
